import type { Metadata } from 'next';
import { AppSidebar } from '@/components/app-sidebar';
import { SidebarInset, SidebarProvider } from '@workspace/ui/components/ui/sidebar';
import { CustomerManagement } from '@/components/customers/customer-management';

export const metadata: Metadata = {
  title: 'Customers - LooLooKids',
  description: 'Manage your customers and their workout credits',
};

export default function CustomersPage() {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
            <CustomerManagement />
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
