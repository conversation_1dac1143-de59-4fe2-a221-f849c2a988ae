import type { Metada<PERSON> } from 'next';
import { AppSidebar } from '@/components/app-sidebar';
import { SidebarInset, SidebarProvider } from '@workspace/ui/components/ui/sidebar';
import { SessionManagementDashboard } from '@/components/admin/session-management-dashboard';

export const metadata: Metadata = {
  title: 'Admin - LooLooKids',
  description: 'Session management and administrative tools',
};

export default function AdminPage() {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
            <SessionManagementDashboard className="p-6" />
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
