'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/ui/card';
import { Button } from '@workspace/ui/components/ui/button';
import { Badge } from '@workspace/ui/components/ui/badge';
import { Skeleton } from '@workspace/ui/components/ui/skeleton';
import useSWRInfinite from 'swr/infinite';
import { getCustomersKey, fetcher, deleteCustomer } from '@/hooks/use-customers';
import { RiEditLine, RiDeleteBinLine, RiCoinLine } from '@remixicon/react';
import type { CustomerResponse } from '@/lib/validations';
import { EditCustomerDialog } from './edit-customer-dialog';
import { DeleteCustomerDialog } from './delete-customer-dialog';
import { PurchaseDialog } from './purchase-dialog';

interface CustomerListProps {
  searchQuery: string;
}

export function CustomerList({ searchQuery }: CustomerListProps) {
  const [editingCustomer, setEditingCustomer] = useState<CustomerResponse | null>(null);
  const [deletingCustomer, setDeletingCustomer] = useState<CustomerResponse | null>(null);
  const [purchaseCustomer, setPurchaseCustomer] = useState<CustomerResponse | null>(null);

  const PAGE_SIZE = 20;
  const getKey = (pageIndex: number, previousPageData: any) => {
    if (previousPageData && !previousPageData.data.length) return null; // reached the end
    return getCustomersKey({ search: searchQuery, limit: PAGE_SIZE, offset: pageIndex * PAGE_SIZE });
  };

  const { data, error, size, setSize, isLoading, isValidating, mutate } = useSWRInfinite(getKey, fetcher);

  const customers = data ? data.flatMap((page: any) => page.data) : [];
  const pagination = data && data.length > 0 ? data[data.length - 1].pagination : { hasMore: false };
  const loading = isLoading || isValidating;

  if (loading && customers.length === 0) {
    return <CustomerListSkeleton />;
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <p>Error loading customers: {error.message || error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (customers.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <p>No customers found.</p>
            {searchQuery && <p className="text-sm">Try adjusting your search terms.</p>}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Customer List</CardTitle>
          <CardDescription>
            {pagination.total} customer{pagination.total !== 1 ? 's' : ''} found
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium">Name</th>
                  <th className="text-left py-3 px-4 font-medium">Contact</th>
                  <th className="text-left py-3 px-4 font-medium">Credits</th>
                  <th className="text-left py-3 px-4 font-medium">Joined</th>
                  <th className="text-right py-3 px-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {customers.map((customer) => (
                  <tr key={customer.id} className="border-b hover:bg-muted/50">
                    <td className="py-3 px-4">
                      <div className="font-medium">{customer.name}</div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="space-y-1">
                        {customer.email && <div className="text-sm text-muted-foreground">{customer.email}</div>}
                        {customer.phone && <div className="text-sm text-muted-foreground">{customer.phone}</div>}
                        {!customer.email && !customer.phone && (
                          <div className="text-sm text-muted-foreground">No contact info</div>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <Badge variant={customer.sessionCredits > 0 ? 'default' : 'secondary'}>
                        {customer.sessionCredits} credits
                      </Badge>
                    </td>
                    <td className="py-3 px-4">
                      <div className="text-sm text-muted-foreground">
                        {new Date(customer.createdAt).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center justify-end gap-2">
                        <Button variant="ghost" size="sm" onClick={() => setPurchaseCustomer(customer)}>
                          <RiCoinLine className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => setEditingCustomer(customer)}>
                          <RiEditLine className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => setDeletingCustomer(customer)}>
                          <RiDeleteBinLine className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {pagination.hasMore && (
            <div className="mt-4 text-center">
              <Button variant="outline" onClick={() => setSize(size + 1)} disabled={loading}>
                {loading ? 'Loading...' : 'Load More'}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialogs */}
      <EditCustomerDialog
        customer={editingCustomer}
        open={!!editingCustomer}
        onOpenChange={(open) => !open && setEditingCustomer(null)}
        onCustomerChanged={() => {
          setEditingCustomer(null);
          mutate(); // revalidate customers
        }}
      />

      <DeleteCustomerDialog
        customer={deletingCustomer}
        open={!!deletingCustomer}
        onOpenChange={(open) => !open && setDeletingCustomer(null)}
        onDelete={async (id: string) => {
          await deleteCustomer(id, { search: searchQuery });
          setDeletingCustomer(null);
          mutate();
        }}
      />

      <PurchaseDialog
        customer={purchaseCustomer}
        open={!!purchaseCustomer}
        onOpenChange={(open) => !open && setPurchaseCustomer(null)}
      />
    </>
  );
}

function CustomerListSkeleton() {
  return (
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-4 w-48" />
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="flex items-center space-x-4">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-48" />
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-24" />
              <div className="flex gap-2 ml-auto">
                <Skeleton className="h-8 w-8" />
                <Skeleton className="h-8 w-8" />
                <Skeleton className="h-8 w-8" />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
