'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@workspace/ui/components/ui/dialog';
import { Button } from '@workspace/ui/components/ui/button';
import { Input } from '@workspace/ui/components/ui/input';
import { Label } from '@workspace/ui/components/ui/label';
import { Badge } from '@workspace/ui/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@workspace/ui/components/ui/tabs';
import { createPurchase } from '@/hooks/use-purchases';
import { createPurchaseSchema, type CreatePurchaseInput, type CustomerResponse } from '@/lib/validations';
import { PurchaseHistory } from './purchase-history';

interface PurchaseDialogProps {
  customer: CustomerResponse | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function PurchaseDialog({ customer, open, onOpenChange }: PurchaseDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<CreatePurchaseInput>({
    resolver: zodResolver(createPurchaseSchema),
    defaultValues: {
      sessionsPurchased: 1,
      amountPaid: 0,
      paymentStatus: 'completed',
    },
  });

  const sessionsPurchased = watch('sessionsPurchased');
  const paymentStatus = watch('paymentStatus');

  const onSubmit = async (data: CreatePurchaseInput) => {
    if (!customer) return;

    setIsSubmitting(true);
    try {
      await createPurchase(customer.id, data);
      reset();
      onOpenChange(false);
    } catch (error) {
      // Error is handled in the hook
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!isSubmitting) {
      onOpenChange(newOpen);
      if (!newOpen) {
        reset();
      }
    }
  };

  // Quick purchase presets
  const quickPurchases = [
    { sessions: 1, price: 25 },
    { sessions: 5, price: 120 },
    { sessions: 10, price: 230 },
    { sessions: 20, price: 440 },
  ];

  const handleQuickPurchase = (sessions: number, price: number) => {
    setValue('sessionsPurchased', sessions);
    setValue('amountPaid', price);
  };

  if (!customer) return null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Manage Credits - {customer.name}</DialogTitle>
          <DialogDescription>Record new purchases and view purchase history</DialogDescription>
        </DialogHeader>

        <div className="mb-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Current Credits:</span>
            <Badge variant={customer.sessionCredits > 0 ? 'default' : 'secondary'}>
              {customer.sessionCredits} credits
            </Badge>
          </div>
        </div>

        <Tabs defaultValue="purchase" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="purchase">New Purchase</TabsTrigger>
            <TabsTrigger value="history">Purchase History</TabsTrigger>
          </TabsList>

          <TabsContent value="purchase" className="space-y-4">
            {/* Quick Purchase Options */}
            <div className="space-y-2">
              <Label>Quick Purchase Options</Label>
              <div className="grid grid-cols-2 gap-2">
                {quickPurchases.map((option) => (
                  <Button
                    key={option.sessions}
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuickPurchase(option.sessions, option.price)}
                    disabled={isSubmitting}
                  >
                    {option.sessions} session{option.sessions !== 1 ? 's' : ''} - ${option.price}
                  </Button>
                ))}
              </div>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sessionsPurchased">Sessions *</Label>
                  <Input
                    id="sessionsPurchased"
                    type="number"
                    min="1"
                    {...register('sessionsPurchased', { valueAsNumber: true })}
                    placeholder="Number of sessions"
                    disabled={isSubmitting}
                  />
                  {errors.sessionsPurchased && (
                    <p className="text-sm text-destructive">{errors.sessionsPurchased.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="amountPaid">Amount Paid *</Label>
                  <Input
                    id="amountPaid"
                    type="number"
                    min="0"
                    step="0.01"
                    {...register('amountPaid', { valueAsNumber: true })}
                    placeholder="0.00"
                    disabled={isSubmitting}
                  />
                  {errors.amountPaid && <p className="text-sm text-destructive">{errors.amountPaid.message}</p>}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="paymentStatus">Payment Status</Label>
                <Select
                  value={paymentStatus}
                  onValueChange={(value) => setValue('paymentStatus', value as any)}
                  disabled={isSubmitting}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="failed">Failed</SelectItem>
                  </SelectContent>
                </Select>
                {errors.paymentStatus && <p className="text-sm text-destructive">{errors.paymentStatus.message}</p>}
              </div>

              {sessionsPurchased && paymentStatus === 'completed' && (
                <div className="rounded-lg border border-green-200 bg-green-50 p-3">
                  <p className="text-sm text-green-800">
                    ✓ {sessionsPurchased} session{sessionsPurchased !== 1 ? 's' : ''} will be added to customer's credit
                    balance
                  </p>
                </div>
              )}

              {paymentStatus === 'pending' && (
                <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-3">
                  <p className="text-sm text-yellow-800">
                    ⏳ Credits will be added when payment status is updated to completed
                  </p>
                </div>
              )}

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => handleOpenChange(false)} disabled={isSubmitting}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? 'Recording...' : 'Record Purchase'}
                </Button>
              </DialogFooter>
            </form>
          </TabsContent>

          <TabsContent value="history">
            <PurchaseHistory customerId={customer.id} />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
