'use client';

import { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/ui/card';
import { Input } from '@workspace/ui/components/ui/input';
import { Badge } from '@workspace/ui/components/ui/badge';
import { Button } from '@workspace/ui/components/ui/button';
import { Skeleton } from '@workspace/ui/components/ui/skeleton';
import { ScrollArea } from '@workspace/ui/components/ui/scroll-area';
import { RiSearchLine, RiFilterLine, RiUserLine } from '@remixicon/react';
import { useCustomersList } from '@/hooks/use-customers';
import { DraggableCustomerItem } from './draggable-customer-item';
import type { CustomerResponse } from '@/lib/validations';

interface CustomerSidebarProps {
  className?: string;
}

export function CustomerSidebar({ className }: CustomerSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [showOnlyWithCredits, setShowOnlyWithCredits] = useState(false);

  // Fetch customers with search
  const { customers, loading, error } = useCustomersList({
    search: searchQuery,
    limit: 100, // Get more customers for the sidebar
  });

  // Filter customers based on credits filter
  const filteredCustomers = useMemo(() => {
    if (!showOnlyWithCredits) return customers;
    return customers.filter((customer) => customer.sessionCredits > 0);
  }, [customers, showOnlyWithCredits]);

  // Group customers by credit status
  const { withCredits, withoutCredits } = useMemo(() => {
    const withCredits: CustomerResponse[] = [];
    const withoutCredits: CustomerResponse[] = [];

    filteredCustomers.forEach((customer) => {
      if (customer.sessionCredits > 0) {
        withCredits.push(customer);
      } else {
        withoutCredits.push(customer);
      }
    });

    return { withCredits, withoutCredits };
  }, [filteredCustomers]);

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <p>Error loading customers</p>
            <p className="text-sm">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <RiUserLine className="h-5 w-5" />
          Customers
        </CardTitle>
        <CardDescription>Drag customers to schedule them for workouts</CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <RiSearchLine className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search customers..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Filter */}
        <div className="flex items-center gap-2">
          <Button
            variant={showOnlyWithCredits ? 'default' : 'outline'}
            size="sm"
            onClick={() => setShowOnlyWithCredits(!showOnlyWithCredits)}
            className="flex items-center gap-1"
          >
            <RiFilterLine className="h-3 w-3" />
            Credits only
          </Button>
          <Badge variant="secondary" className="text-xs">
            {filteredCustomers.length} customers
          </Badge>
        </div>

        {/* Customer List */}
        <ScrollArea className="h-[calc(100svh-310px)] -mx-6 px-6 pb-2">
          <div className="space-y-2">
            {loading ? (
              // Loading skeleton
              Array.from({ length: 5 }).map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2 flex-1">
                        <Skeleton className="h-4 w-4 rounded-full" />
                        <div className="space-y-1 flex-1">
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-3 w-32" />
                        </div>
                      </div>
                      <Skeleton className="h-5 w-8" />
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <>
                {/* Customers with credits */}
                {withCredits.length > 0 && (
                  <>
                    {withCredits.map((customer) => (
                      <DraggableCustomerItem key={customer.id} customer={customer} />
                    ))}
                  </>
                )}

                {/* Customers without credits */}
                {withoutCredits.length > 0 && !showOnlyWithCredits && (
                  <>
                    {withCredits.length > 0 && (
                      <div className="py-2">
                        <div className="text-xs text-muted-foreground font-medium">No Credits</div>
                      </div>
                    )}
                    {withoutCredits.map((customer) => (
                      <DraggableCustomerItem key={customer.id} customer={customer} canDrag={false} />
                    ))}
                  </>
                )}

                {/* Empty state */}
                {filteredCustomers.length === 0 && !loading && (
                  <div className="text-center py-8 text-muted-foreground">
                    <RiUserLine className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">{searchQuery ? 'No customers found' : 'No customers available'}</p>
                  </div>
                )}
              </>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
