'use client';

import { useEffect, useState } from 'react';
import { Badge } from '@workspace/ui/components/ui/badge';
import { Button } from '@workspace/ui/components/ui/button';
import { RiCheckLine, RiCloseLine, RiTimeLine, RiAlertLine, RiGroupLine } from '@remixicon/react';
import { notify } from '@/lib/notification-service';
import type { WorkoutResponse } from '@/lib/validations';

interface SessionStatusIndicatorProps {
  workout: WorkoutResponse;
  onStatusChange?: (workoutId: string, newStatus: string) => void;
  showActions?: boolean;
  className?: string;
}

export function SessionStatusIndicator({
  workout,
  onStatusChange,
  showActions = false,
  className,
}: SessionStatusIndicatorProps) {
  const [warnings, setWarnings] = useState<string[]>([]);

  useEffect(() => {
    const checkWarnings = () => {
      const newWarnings: string[] = [];
      const participantCount = workout.participantCount || 0;

      // Check participant requirements
      if (participantCount < workout.minParticipants) {
        const needed = workout.minParticipants - participantCount;
        newWarnings.push(`Need ${needed} more participant${needed > 1 ? 's' : ''} to meet minimum`);
      }

      // Check capacity
      if (participantCount === workout.maxParticipants) {
        newWarnings.push('At maximum capacity');
      } else if (participantCount === workout.maxParticipants - 1) {
        newWarnings.push('1 spot remaining');
      }

      // Check timing
      const now = new Date();
      const sessionTime = new Date(workout.startTime);
      const hoursUntil = (sessionTime.getTime() - now.getTime()) / (1000 * 60 * 60);

      if (hoursUntil < 2 && hoursUntil > 0) {
        newWarnings.push('Session starts soon');
      } else if (hoursUntil <= 0) {
        newWarnings.push('Session has started');
      }

      setWarnings(newWarnings);
    };

    checkWarnings();
    const interval = setInterval(checkWarnings, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [workout]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'confirmed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <RiTimeLine className="h-3 w-3" />;
      case 'confirmed':
        return <RiCheckLine className="h-3 w-3" />;
      case 'completed':
        return <RiCheckLine className="h-3 w-3" />;
      case 'cancelled':
        return <RiCloseLine className="h-3 w-3" />;
      default:
        return <RiTimeLine className="h-3 w-3" />;
    }
  };

  const canConfirm = () => {
    const participantCount = workout.participantCount || 0;
    return workout.status === 'scheduled' && participantCount >= workout.minParticipants;
  };

  const canCancel = () => {
    return workout.status === 'scheduled' || workout.status === 'confirmed';
  };

  const handleConfirm = () => {
    if (onStatusChange) {
      onStatusChange(workout.id, 'confirmed');
    }
  };

  const handleCancel = () => {
    if (onStatusChange) {
      onStatusChange(workout.id, 'cancelled');
    }
  };

  const handleComplete = () => {
    if (onStatusChange) {
      onStatusChange(workout.id, 'completed');
      notify.sessionCompleted(workout.title);
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Status Badge */}
      <div className="flex items-center gap-2">
        <Badge className={`${getStatusColor(workout.status)} flex items-center gap-1`}>
          {getStatusIcon(workout.status)}
          {workout.status}
        </Badge>

        {/* Participant Count */}
        <div className="flex items-center gap-1 text-sm text-muted-foreground">
          <RiGroupLine className="h-4 w-4" />
          <span>
            {workout.participantCount || 0}/{workout.maxParticipants}
          </span>
        </div>
      </div>

      {/* Warnings */}
      {warnings.length > 0 && (
        <div className="space-y-1">
          {warnings.map((warning, index) => (
            <div key={index} className="flex items-center gap-2 text-sm text-amber-600">
              <RiAlertLine className="h-4 w-4" />
              {warning}
            </div>
          ))}
        </div>
      )}

      {/* Action Buttons */}
      {showActions && (
        <div className="flex items-center gap-2">
          {workout.status === 'scheduled' && (
            <>
              <Button
                size="sm"
                onClick={handleConfirm}
                disabled={!canConfirm()}
                className="text-green-600 hover:text-green-700"
                variant="outline"
              >
                <RiCheckLine className="h-3 w-3 mr-1" />
                Confirm
              </Button>

              {canCancel() && (
                <Button size="sm" variant="outline" onClick={handleCancel} className="text-red-600 hover:text-red-700">
                  <RiCloseLine className="h-3 w-3 mr-1" />
                  Cancel
                </Button>
              )}
            </>
          )}

          {workout.status === 'confirmed' && (
            <>
              <Button
                size="sm"
                onClick={handleComplete}
                className="text-blue-600 hover:text-blue-700"
                variant="outline"
              >
                <RiCheckLine className="h-3 w-3 mr-1" />
                Complete
              </Button>

              {canCancel() && (
                <Button size="sm" variant="outline" onClick={handleCancel} className="text-red-600 hover:text-red-700">
                  <RiCloseLine className="h-3 w-3 mr-1" />
                  Cancel
                </Button>
              )}
            </>
          )}

          {!canConfirm() && workout.status === 'scheduled' && (
            <span className="text-xs text-muted-foreground">Cannot confirm: minimum participants not met</span>
          )}
        </div>
      )}
    </div>
  );
}
