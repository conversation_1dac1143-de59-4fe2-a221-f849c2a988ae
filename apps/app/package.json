{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "registry:build": "shadcn build"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@remixicon/react": "^4.6.0", "@workspace/auth": "workspace:*", "@workspace/ui": "workspace:*", "date-fns": "^4.1.0", "drizzle-orm": "^0.38.4", "lucide-react": "0.487.0", "next": "15.2.4", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "recharts": "^3.0.2", "shadcn": "^2.4.0", "sonner": "^2.0.3", "swr": "^2.3.4", "zod": "^3.25.70"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.1", "@types/node": "^22.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "postcss": "^8.5.3", "tailwindcss": "^4.1.1", "tw-animate-css": "^1.2.5", "typescript": "^5.8.2"}}