{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "registry:build": "shadcn build"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.1.8", "@remixicon/react": "^4.6.0", "@workspace/auth": "workspace:*", "@workspace/ui": "workspace:*", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.38.4", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "0.487.0", "next": "15.2.4", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "react-resizable-panels": "^3.0.3", "recharts": "^3.0.2", "shadcn": "^2.4.0", "sonner": "^2.0.3", "swr": "^2.3.4", "tailwind-merge": "^3.1.0", "vaul": "^1.1.2", "zod": "^3.25.70"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.1", "@types/node": "^22.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "postcss": "^8.5.3", "tailwindcss": "^4.1.1", "tw-animate-css": "^1.2.5", "typescript": "^5.8.2"}}